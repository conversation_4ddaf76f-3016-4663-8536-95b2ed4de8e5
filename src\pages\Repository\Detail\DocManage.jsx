import { useState, useEffect, useRef } from 'react'
import { Button, Space, Popover, Input, Tree, Table, Switch, message, Modal, Select } from 'antd'
import {
  DownOutlined,
  EllipsisOutlined,
  PlusOutlined,
  EditOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import { DndProvider, useDrag, useDrop } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import {
  getRepoFolders,
  updateRepoFolders,
  updateDocFolder,
  getDocList,
  buildDoc,
  buildRepo,
  updateParse,
  delDoc,
  changeDoc,
  downloadDoc
} from '../service'
import { getDisplayStatus, displayStatusMap, formatFileSize } from '../utils'
import styles from './style.module.scss'
import DocAddModal from './DocAddModal'
import { LinkTree } from '@/components'
import { useRepoStore } from '../repoStore'
import RenameModal from './RenameModal'
import DeleteModal from '../DeleteModal'
import { APP_ENV } from '@/utils/constant'

const { Search } = Input

const DocManage = () => {
  const { repoId } = useParams()
  const navigate = useNavigate()
  const { setRefreshMethod, fileTypes } = useRepoStore()
  const [treeData, setTreeData] = useState([])
  const [expandTree, setExpandTree] = useState([])
  const [selectedTree, setSelectedTree] = useState(['all'])
  const [docList, setDocList] = useState([])
  const [total, setTotal] = useState(0)
  const [pageData, setPageData] = useState({ pageIndex: 1, pageSize: 10 })
  const [timeSort, setTimeSort] = useState() //ascend升序 descend降序
  const [visible, setVisble] = useState(false)
  const [fileCurrent, setFileCurrent] = useState()
  const [docName, setDocName] = useState('')
  const [docTypes, setDocTypes] = useState([])
  const [status, setStatus] = useState([])
  const [openContent, setOpenContent] = useState(false)
  const [renameVisible, setRenameVisible] = useState(false)

  const [delVisible, setDelVisible] = useState(false)
  const [modalConfig, setModalConfig] = useState()

  const statusList = ['解析中', '解析失败', '待发布', '构建中', '构建失败', '发布成功', '已失效']
  const refreshRef = useRef(null)
  const frequencyMap = {
    NONE: '不更新',
    DAY: '每日更新',
    WEEK: '每周更新',
    MONTH: '每月更新'
  }
  refreshRef.current = () => {
    let params = {
      repoId,
      folderId: selectedTree[0] === 'all' ? '' : selectedTree[0],
      docName,
      docType: docTypes?.length > 0 ? docTypes.join(',') : undefined,
      status: status?.length > 0 ? status.join(',') : undefined,
      ...pageData
    }
    if (timeSort) {
      params['orderByField'] = 'update_time'
      params['isAsc'] = timeSort === 'ascend' ? true : false
    } else {
      params['orderByField'] = 'create_time'
    }
    if (openContent) {
      delete params['docName']
      params['searchContent'] = docName
    }
    getDocList(params).then((data) => {
      let res = data.data
      if (res.data.docPage.records.length === 0 && pageData.pageIndex > 1) {
        setPageData({
          ...pageData,
          pageIndex: pageData.pageIndex - 1
        })
        return
      }
      setDocList(res.data.docPage.records)
      setTotal(res.data.docPage.total)
    })
  }

  const getLabels = () => {
    getRepoFolders({
      repoId
    }).then((data) => {
      let res = data.data
      // console.log('获取标签数据',res);
      let labelData = res.data.folderList
      let treeLabelData = [
        {
          title: '全部',
          key: 'all',
          children: labelData.map((item) => ({
            key: item.id,
            title: item.folderName
          }))
        }
      ]
      setTreeData(treeLabelData)
      setExpandTree(['all'])
    })
  }
  const getDocs = () => {
    let params = {
      repoId,
      folderId: selectedTree[0] === 'all' ? '' : selectedTree[0],
      docName,
      docType: docTypes?.length > 0 ? docTypes.join(',') : undefined,
      status: status?.length > 0 ? status.join(',') : undefined,
      ...pageData
    }
    if (timeSort) {
      params['orderByField'] = 'update_time'
      params['isAsc'] = timeSort === 'ascend' ? true : false
    } else {
      params['orderByField'] = 'create_time'
    }
    if (openContent) {
      delete params['docName']
      params['searchContent'] = docName
    }
    getDocList(params).then((data) => {
      let res = data.data
      if (res.data.docPage.records.length === 0 && pageData.pageIndex > 1) {
        setPageData({
          ...pageData,
          pageIndex: pageData.pageIndex - 1
        })
        return
      }
      setDocList(res.data.docPage.records)
      setTotal(res.data.docPage.total)
    })
  }
  const onTreeSearch = (value) => {
    console.log('tree search', value)
  }
  const onDocSearch = (value) => {
    setDocName(value)
  }
  const onNodeSelect = (keys, e) => {
    // console.log('树节点点击', keys, e)
    setSelectedTree(keys)
  }
  const openRename = (e, record) => {
    e.stopPropagation()
    setFileCurrent(record)
    setRenameVisible(true)
  }
  const jumpToManage = (e, record) => {
    console.log('详情跳转', record)
    navigate(`/repo/${repoId}/chunkManage?docId=${record.id}`)
  }
  useEffect(() => {
    getLabels()
    // getDocs()
    setRefreshMethod(getDocs) //对外提供刷新方法，构建后会调用

    let timerId = setInterval(() => {
      refreshRef.current()
    }, 10000)
    return () => {
      setRefreshMethod(null)
      clearInterval(timerId)
    }
  }, [])

  useEffect(() => {
    getDocs()
    setRefreshMethod(getDocs)
  }, [selectedTree, pageData, timeSort, docName, docTypes, status, openContent])

  const filterContent = (
    <div style={{ width: '400px', padding: '8px' }}>
      <Select
        mode="multiple"
        value={docTypes}
        onChange={(val) => setDocTypes(val)}
        options={fileTypes.map((item) => ({ value: item, label: item }))}
        placeholder="请选择文档格式"
        style={{ width: '100%', marginBottom: '8px' }}
        prefix={
          <label style={{ borderRight: '1px solid #e7e9ed', paddingRight: '8px' }}>文档格式</label>
        }
      />
      <Select
        mode="multiple"
        value={status}
        onChange={(val) => setStatus(val)}
        options={statusList.map((item) => ({ value: item, label: item }))}
        placeholder="请选择知识状态"
        style={{ width: '100%', marginBottom: '8px' }}
        prefix={
          <label style={{ borderRight: '1px solid #e7e9ed', paddingRight: '8px' }}>知识状态</label>
        }
      />
      <Select
        value={openContent}
        onChange={(val) => setOpenContent(val)}
        options={[
          { value: true, label: '开启' },
          { value: false, label: '关闭' }
        ]}
        placeholder="请选择是否开启内容搜索"
        style={{ width: '100%' }}
        prefix={
          <label style={{ borderRight: '1px solid #e7e9ed', paddingRight: '8px' }}>内容搜索</label>
        }
      />
    </div>
  )
  //
  const ItemTypes = {
    LIST_ITEM: 'list_item'
  }

  const DraggableDocItem = (props) => {
    const { text, record } = props
    const [{ isDragging }, drag] = useDrag(() => ({
      type: ItemTypes.LIST_ITEM,
      // item: { id, name },
      item: record,
      collect: (monitor) => ({
        isDragging: monitor.isDragging()
      })
    }))
    return (
      <div ref={drag} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <a className={styles.docName} onClick={(e) => jumpToManage(e, record)}>
          {record.docName}
          <EditOutlined className={styles.icon} onClick={(e) => openRename(e, record)} />
        </a>
      </div>
    )
  }

  //
  const LabelNode = (props) => {
    const { nodeData } = props
    const inputRef = useRef()
    const [isEdit, setEdit] = useState(false)
    const [textValue, setTextValue] = useState('')
    const [{ canDrop, isOver }, drop] = useDrop(() => ({
      accept: ItemTypes.LIST_ITEM,
      drop: (item, monitor) => {
        // item为文件数据 nodeData为节点数据
        console.log('dropResult', item, nodeData)
        updateDocFolder({
          docId: item.id,
          folderId: nodeData.key == 'all' ? '' : nodeData.key
        }).then((data) => {
          let res = data.data
          if (res.code == 0) {
            message.success('操作成功')
          }
          getDocs()
        })
      },
      collect: (monitor) => ({
        isOver: monitor.isOver()
      })
    }))

    useEffect(() => {
      if (nodeData.isNew) {
        setEdit(true)
      }
    }, [nodeData])

    useEffect(() => {
      if (isEdit) {
        console.log('ref', inputRef.current)
        inputRef.current.focus()
      }
    }, [isEdit])

    const editLabel = (e) => {
      e.stopPropagation()
      setEdit(true)
      setTextValue(nodeData.title)
    }
    const copyLabel = (e) => {
      e.stopPropagation()
      updateRepoFolders({
        repoId,
        copyFolders: JSON.stringify([nodeData.key])
      }).then((data) => {
        let res = data.data
        if (res.code == 0) {
          message.success('操作成功')
        }
        getLabels()
      })
    }
    const delLabel = (e) => {
      e.stopPropagation()
      setModalConfig({
        titleName: nodeData.title,
        content: (
          <span>
            <strong>将删除该标签下的所有文件</strong>，请确认是否要删除
          </span>
        ),
        delAction: async () => {
          let data = await updateRepoFolders({
            repoId,
            delFolders: JSON.stringify([nodeData.key])
          })
          if (data.data.code == 0) {
            message.success('操作成功')
          }
          setDelVisible(false)
          getLabels()
          setSelectedTree(['all'])
        }
      })
      setDelVisible(true)
    }
    const exitEdit = async () => {
      setEdit(false)
      if (!textValue || textValue.length === 0) {
        getLabels()
        return
      }
      if (nodeData.isNew) {
        // 创建新标签
        try {
          let data = await updateRepoFolders({
            repoId,
            addFolders: JSON.stringify([textValue])
          })
          let res = data.data
          if (res.code == 0) {
            message.success('添加成功')
          }
        } catch (e) {
          getLabels()
        }
      } else {
        try {
          let data = await updateRepoFolders({
            repoId,
            updateFolders: JSON.stringify([{ id: nodeData.key, updateFolder: textValue }])
          })
          let res = data.data
          if (res.code == 0) {
            message.success('操作成功')
          }
        } catch (e) {
          getLabels()
        }
      }
      getLabels()
    }
    const addChild = (e) => {
      e.stopPropagation()
      let treeCurrent = JSON.parse(JSON.stringify(treeData))
      treeCurrent[0]['children'].unshift({
        key: 'new' + new Date(),
        title: '',
        isNew: true
      })
      // console.log('treeData', treeCurrent, setTreeData)
      setTreeData(treeCurrent)
    }

    const getPopContent = (keyValue) => {
      if (keyValue === 'all') {
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <Button type="text" onClick={(e) => editLabel(e)} disabled>
              编辑
            </Button>
            <Button type="text" onClick={(e) => addChild(e)}>
              新增标签
            </Button>
          </div>
        )
      } else {
        return (
          <div style={{ display: 'flex', flexDirection: 'column' }}>
            <Button type="text" onClick={(e) => editLabel(e)}>
              编辑
            </Button>

            <Button type="text" onClick={(e) => copyLabel(e)}>
              {APP_ENV === 'auto' ? '复制' : '复刻'}
            </Button>
            <Button type="text" onClick={(e) => delLabel(e)}>
              删除
            </Button>
          </div>
        )
      }
    }

    return (
      <div ref={drop} className={`${styles.labelNode} ${isOver ? styles.labelHover : ''}`}>
        {isEdit ? (
          <Input
            ref={inputRef}
            value={textValue}
            onChange={(e) => setTextValue(e.target.value)}
            onClick={(e) => e.stopPropagation()}
            onPressEnter={(e) => exitEdit()}
            onBlur={() => exitEdit()}
          />
        ) : (
          <span>{nodeData.title}</span>
        )}
        {!nodeData.isNew && !isEdit && (
          <Popover
            placement="bottom"
            content={getPopContent(nodeData.key)}
            arrow={false}
            trigger="click"
          >
            <EllipsisOutlined onClick={(e) => e.stopPropagation()} />
          </Popover>
        )}
      </div>
    )
  }
  const addNew = () => {
    setFileCurrent(null)
    setVisble(true)
  }
  const editFile = (record) => {
    // console.log('当前record', record);
    setFileCurrent(record)
    setVisble(true)
  }
  const downloadFile = (record) => {
    //docName filePath
    downloadDoc({
      fileUrl: record.filePath,
      fileDownName: record.docName
    })
      .then((response) => {
        const blob = new Blob([response.data])
        const downloadLink = document.createElement('a')
        const url = window.URL.createObjectURL(blob)
        downloadLink.href = url
        downloadLink.download = record.docName
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)
        window.URL.revokeObjectURL(url)
      })
      .catch((err) => {
        console.error('文件下载失败', err)
        message.error('文件下载失败，请稍后重试')
      })
  }
  const handleUpdate = (docId, status) => {
    if (status === 7) {
      // 构建失败情形，需要重新构建
      buildDoc({ docId }).then((data) => {
        let res = data.data
        if (res.code == 0) {
          getDocs()
        }
      })
    } else {
      updateParse({ docId }).then((data) => {
        let res = data.data
        if (res.code == 0) {
          message.success('更新成功')
          getDocs()
        }
      })
    }
  }
  const openFileDel = async (record) => {
    setFileCurrent(record)
    console.log('Confirmed: record', record)
    setModalConfig({
      titleName: record.docName,
      content: (
        <span>
          <strong>删除文档后无法恢复</strong>，建议优先考虑文档失效功能。 确定删除文档？
        </span>
      ),
      delAction: async () => {
        let data = await delDoc({ docId: record.id })
        if (data.data.code == 0) {
          message.success('文件删除成功')
        }
        setDelVisible(false)
        getDocs()
      }
    })
    setDelVisible(true)
  }
  const handleStatusChange = (val, record) => {
    changeDoc({
      docId: record.id,
      isAble: val
    }).then((data) => {
      let res = data.data
      getDocs()
    })
  }
  const columns = [
    {
      title: '文件名',
      key: 'docName',
      // minWidth: 120,
      width: 220,
      fixed: 'left',
      render: (text, record) => <DraggableDocItem text={text} record={record} />
    },
    {
      title: '知识数',
      dataIndex: 'knowledgeCount',
      key: 'knowledgeCount',
      width: 100
    },
    {
      title: '更新频率',
      dataIndex: 'updateFrequency',
      key: 'updateFrequency',
      width: 100,
      render: (text, record) => <span>{frequencyMap[text] || '---'}</span>
    },
    {
      title: '大小',
      key: 'fileSize',
      width: 100,
      render: (text, record) => <span>{formatFileSize(record.fileSize)}</span>
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 180,
      sorter: true
    },
    {
      title: '知识状态',
      key: 'status',
      width: 110,
      render: (text, record) => {
        const status = getDisplayStatus(record)
        const statusInfo = displayStatusMap[status]
        return (
          <div style={statusInfo.textStyle}>
            {statusInfo.text}
            {statusInfo.allowRetry && (
              <ReloadOutlined
                style={{ marginLeft: '4px', cursor: 'pointer' }}
                onClick={() => handleUpdate(record.id, status)}
              />
            )}
          </div>
        )
      }
    },
    {
      title: '是否生效',
      key: 'isAble',
      width: 100,
      render: (text, record) => {
        const status = getDisplayStatus(record)
        return (
          <Switch
            checked={record.isAble === 1}
            disabled={status === 4 || status === 7 || status === 3}
            onChange={(checked) => handleStatusChange(checked ? 1 : 0, record)}
          />
        )
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (text, record) => (
        <>
          <a onClick={() => editFile(record)} style={{ marginRight: 12 }}>
            编辑
          </a>
          {record.docType != 'url' && (
            <a onClick={() => downloadFile(record)} style={{ marginRight: 12 }}>
              下载原文
            </a>
          )}
          {/* todo: 状态判断 */}
          {record.docType == 'url' && (
            <a onClick={() => handleUpdate(record.id)} style={{ marginRight: 12 }}>
              更新
            </a>
          )}
          <a onClick={() => openFileDel(record)} style={{ color: '#ff4d4d' }}>
            删除
          </a>
        </>
      )
    }
  ]
  const onTableChange = (pagination, filters, sorter) => {
    // console.log('params', pagination, sorter.order)
    let order = sorter.order //ascend升序 descend降序
    setTimeSort(order)
  }
  const changeFilter = () => {
    setAdvanceFlag(!advancedFlag)
  }
  const filterItems = [
    {
      key: '1',
      label: (
        <Select>
          <Select.Option value="sample">sample</Select.Option>
        </Select>
      )
    }
  ]

  return (
    <div className={styles.docManage}>
      <div className={styles.main}>
        <DndProvider backend={HTML5Backend}>
          <div className={styles.rightMain}>
            <div className={styles.tagLeft}>
              {/* <Search
                style={{
                  marginBottom: 8
                }}
                placeholder="请输入标签名称"
                onSearch={onTreeSearch}
              /> */}
              <LinkTree
                blockNode
                expandedKeys={expandTree}
                selectedKeys={selectedTree}
                treeData={treeData}
                titleRender={(nodeData) => <LabelNode nodeData={nodeData} />}
                rootStyle={{ backgroud: 'none' }}
                onSelect={(keys, e) => onNodeSelect(keys, e)}
                onExpand={(expandedKeys) => setExpandTree(expandedKeys)}
              />
            </div>

            <div className={styles.docRight}>
              <Button onClick={addNew} icon={<PlusOutlined />}>
                添加文档
              </Button>
              <Search
                style={{
                  marginLeft: 8,
                  width: '400px'
                }}
                placeholder={`请输入文档${openContent ? '内容' : '名称'}搜索`}
                onSearch={onDocSearch}
                suffix={
                  // <Button type="link" onClick={() => changeFilter()}>
                  //   高级筛选
                  // </Button>
                  <Popover content={filterContent} title={null} trigger="click" placement="bottom">
                    <Button type="link">高级筛选</Button>
                  </Popover>
                }
              />

              <Table
                columns={columns}
                dataSource={docList}
                onChange={onTableChange}
                rowKey="id"
                scroll={{
                  // x: 'max-content'
                  x: '100%'
                }}
                pagination={{
                  pageSize: pageData.pageSize,
                  current: pageData.pageIndex,
                  total: total,
                  hideOnSinglePage: true,
                  onChange: (page, pageSize) => {
                    setPageData({
                      pageSize,
                      pageIndex: page
                    })
                  }
                }}
              />
            </div>
          </div>
        </DndProvider>
      </div>

      {visible && (
        <DocAddModal
          visible={visible}
          setVisble={setVisble}
          fileCurrent={fileCurrent}
          setFileCurrent={setFileCurrent}
          getDocs={getDocs}
        />
      )}

      {renameVisible && (
        <RenameModal
          visible={renameVisible}
          setVisible={setRenameVisible}
          fileInfo={fileCurrent}
          getDocs={getDocs}
        />
      )}

      {delVisible && (
        <DeleteModal visible={delVisible} setVisible={setDelVisible} modalConfig={modalConfig} />
      )}
    </div>
  )
}

export default DocManage
