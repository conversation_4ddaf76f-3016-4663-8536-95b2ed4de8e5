import { <PERSON><PERSON><PERSON>, Col, Row, Space, Button, Switch } from 'antd'
import styles from './style.module.scss'
import { useEffect, useState } from 'react'
import { useConfigRightResource } from '../hooks/useConfigRight'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'

import { RightOutlined, TagOutlined } from '@ant-design/icons'

function TopicConfig() {
  const [activeKey, setActiveKey] = useState(['topic'])

  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const routerId = useAgentDetailStore((state) => state.routerId)

  const [topicGeneration, setTopicGeneration] = useState(false)

  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && routerId) {
      getTopicConfig(agentDetail?.boxBot?.botId, routerId)
    }
  }, [agentDetail?.boxBot?.botId, routerId])

  const getTopicConfig = async (boxBotId, routerId) => {
    const res = await ajax({
      url: '/bot/config/getAbleAndSearchConfig',
      data: {
        botId: boxBotId,
        routerId
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      setTopicGeneration(Number(res.data?.data?.topicGeneration) === 1)
    }
  }

  const onAddClick = (e) => {
    e.stopPropagation()
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const onTopCheckedChange = async (checked) => {
    console.log('checked', checked)
    setLoading(true)
    try {
      const res = await ajax({
        url: '/bot/config/saveAbleAndSearchConfig',
        data: {
          botId: agentDetail?.boxBot?.botId,
          topicGeneration: checked ? 1 : 0
        },
        method: 'post'
      })
      if (res.data.code === '0') {
        setTopicGeneration(checked)
      }
    } catch (e) {
    } finally {
      setLoading(false)
    }
  }

  const items = [
    {
      key: 'topic',

      label: (
        <div className={styles.labelWithIcon}>
          <TagOutlined className={styles.arrow} />
          <span>话题推荐</span>
        </div>
      ),

      extra: (
        <Space size={4}>
          <Switch checked={topicGeneration} loading={loading} onChange={onTopCheckedChange} />
        </Space>
      )
    }
  ]

  const onChange = (val) => {
    // setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        size="small"
        ghost
        activeKey={activeKey}
        onChange={onChange}
        expandIcon={() => null}
      />
    </>
  )
}

export default TopicConfig
