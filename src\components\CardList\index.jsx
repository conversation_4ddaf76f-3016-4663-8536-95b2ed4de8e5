import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useCallback
} from 'react'
import { Input, <PERSON><PERSON>, Spin } from 'antd'
import ajax from '@/utils/http'
import PropTypes from 'prop-types'
import Card from './Card'
import styles from './style.module.scss'
import { LoadingOutlined } from '@ant-design/icons'
import { CustomEmpty } from '@/components'

const CardList = forwardRef(
  ({ title, searchConfig, cardConfig, dropdown, renderFooterTag, events }, ref) => {
    const [data, setData] = useState([])
    const [loading, setLoading] = useState(false)
    const [hasMore, setHasMore] = useState(true)
    const [currentPage, setCurrentPage] = useState(1)
    const [searchValue, setSearchValue] = useState('')
    const [isInitialLoading, setIsInitialLoading] = useState(true)
    const scrollContainerRef = useRef(null)
    const pageSize = 9

    const fetchData = async (page = 1, isLoadMore = false) => {
      if (loading) return // 防止重复请求
      setLoading(true)
      try {
        const response = await ajax({
          url: searchConfig.url,
          method: searchConfig.method || 'get',
          data: {
            [searchConfig.searchKey]: searchValue,
            [searchConfig.pagination.page]: page,
            [searchConfig.pagination.pageSize]: pageSize,
            ...(searchConfig.extraParams || {})
          },
          ...searchConfig.httpConfig
        })

        // 如果是加载更多，添加500ms延迟让用户看到加载提示
        if (isLoadMore) {
          await new Promise((resolve) => setTimeout(resolve, 500))
        }

        const formattedData = searchConfig.dataFormatter(response.data)

        if (isLoadMore) {
          // 加载更多时，追加数据并更新hasMore状态
          setData((prevData) => {
            const newData = [...prevData, ...formattedData.list]
            setHasMore(newData.length < formattedData.total)
            return newData
          })
        } else {
          // 初始加载或搜索时，重置数据
          setData(formattedData.list)
          setCurrentPage(1)
          setHasMore(formattedData.list.length < formattedData.total)
        }
      } catch (error) {
        console.error('Fetch data error:', error)
        setHasMore(false)
      } finally {
        setLoading(false)
        setIsInitialLoading(false)
      }
    }

    // 滚动监听函数
    const handleScroll = useCallback(() => {
      if (!scrollContainerRef.current || loading || !hasMore) {
        return
      }

      const scrollContainer = scrollContainerRef.current
      const { scrollTop, scrollHeight, clientHeight } = scrollContainer
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight

      // 当滚动到距离底部5px时触发加载（更精确的触发时机）
      if (distanceFromBottom <= 5) {
        const nextPage = currentPage + 1
        setCurrentPage(nextPage)
        fetchData(nextPage, true)
      }
    }, [loading, hasMore, currentPage, fetchData])

    // 搜索时重新加载数据（包括初始加载）
    useEffect(() => {
      setIsInitialLoading(true) // 搜索时显示全屏加载
      setCurrentPage(1)
      setHasMore(true)
      fetchData(1, false)
    }, [searchValue])

    // 添加滚动监听
    useEffect(() => {
      const scrollContainer = scrollContainerRef.current
      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll)
        return () => scrollContainer.removeEventListener('scroll', handleScroll)
      }
    }, [handleScroll])

    useImperativeHandle(ref, () => ({
      reload: () => {
        setIsInitialLoading(true) // 手动刷新时显示全屏加载
        setCurrentPage(1)
        setHasMore(true)
        fetchData(1, false)
      }
    }))

    const handleSearch = (value) => {
      setSearchValue(value)
    }

    return (
      <div className={styles.cardListContainer}>
        <div className={styles.header}>
          <h2>我的{title}</h2>
          <div className={styles.actions}>
            <Input.Search
              onSearch={handleSearch}
              placeholder={`搜索${title}...`}
              style={{ width: 206, marginRight: 14 }}
              allowClear
            />
            <Button type="primary" onClick={events?.onAddClick}>
              +&nbsp;创建{title}
            </Button>
          </div>
        </div>

        {/* 初始加载状态 */}
        {isInitialLoading ? (
          <Spin size="large" indicator={<LoadingOutlined spin />} className={styles.loading} />
        ) : data.length === 0 ? (
          /* 没有数据的空状态 */
          <CustomEmpty />
        ) : (
          /* 有数据时的滚动容器 */
          <div className={styles.scrollContainer} ref={scrollContainerRef}>
            <div className={styles['card-list']}>
              {data.map((item) => {
                let conf = {}
                Object.keys(cardConfig).forEach((k) => {
                  if (item[cardConfig[k]]) {
                    conf[k] = item[cardConfig[k]]
                  }
                })
                return (
                  <Card
                    item={item}
                    key={item.id}
                    dropdown={dropdown}
                    renderFooterTag={renderFooterTag}
                    events={events}
                    {...conf}
                  ></Card>
                )
              })}
            </div>

            {/* 加载更多指示器 */}
            {loading && data.length > 0 && !isInitialLoading && (
              <div className={styles.loadingMore}>
                <LoadingOutlined style={{ fontSize: 16 }} />
                <span>加载中...</span>
              </div>
            )}

            {/* 没有更多数据提示 - 只有在加载过多页数据后才显示 */}
            {!hasMore && data.length > 0 && currentPage > 1 && (
              <div className={styles.noMore}>没有更多数据了</div>
            )}
          </div>
        )}
      </div>
    )
  }
)

CardList.propTypes = {
  title: PropTypes.string.isRequired,
  searchConfig: PropTypes.shape({
    url: PropTypes.string.isRequired,
    method: PropTypes.oneOf(['get', 'post']).isRequired,
    dataFormatter: PropTypes.func.isRequired,
    pluginTypes: PropTypes.array
  }),
  cardConfig: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    updateTime: PropTypes.string,
    logo: PropTypes.string
  }),
  dropdown: PropTypes.shape({
    optItems: PropTypes.array.isRequired,
    onDropItemsClick: PropTypes.func.isRequired
  }),
  events: PropTypes.shape({
    onAddClick: PropTypes.func.isRequired
  })
}

export default CardList
