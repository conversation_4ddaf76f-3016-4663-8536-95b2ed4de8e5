import React, { useState, useEffect, useRef } from 'react'
import {
  Modal,
  Table,
  Select,
  Button,
  Checkbox,
  message,
  Tag,
  Tooltip,
  Empty,
  Flex,
  Pagination,
  Input
} from 'antd'
import { useNavigate } from 'react-router-dom'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { cloneDeep, max } from 'lodash'
import ajax from '@/utils/http'
import IntentModal from '../../Common/IntentModal'
import { APP_ENV } from '@/utils/constant'
import { TagOutlined } from '@ant-design/icons'
import IconReactAi from 'assets/svgs/react-ai.svg?react'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import microAppRouter from '@/hooks/useMicroAppRouter.js'
import SceneLocalizationSelector from '@/components/SceneLocalizationSelector'
import CenterLoading from '@/components/CenterLoading'
import { CustomEmpty } from '@/components'

const { Option } = Select
const { Search } = Input

const PLUGINS_MAP = {
  0: '汽车模板',
  1: 'openAPI协议',
  3: 'coze协议',
  4: '星辰协议'
}

const PLUGIN_COLOR_MAP = {
  0: 'blue',
  1: 'green',
  3: 'purple',
  4: 'purple'
}

const WorkflowConfigModal = ({ visible, onClose, onRefresh }) => {
  const { baseRouter } = microAppRouter()
  const navigate = useNavigate()
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  const [selectedKeys, setSelectedKeys] = useState([])
  const [originalSelectedKeys, setOriginalSelectedKeys] = useState([])

  const originFlows = useRef([])
  const [domains, setDomains] = useState([])
  const [flows, setFlows] = useState([])
  const [bussinessId, setBussinessId] = useState('')

  const [intentModalOpen, setIntentModalOpen] = useState(false)

  const [loading, setLoading] = useState(false)

  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10 // 每页10条数据

  // 计算当前页数据
  const currentData = flows.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && visible) {
      getFlows(agentDetail?.boxBot?.botId)
      getDomains(agentDetail?.boxBot?.botId)
    } else {
      setFlows([])
      setCurrentPage(1)
    }
  }, [visible, agentDetail])

  const getDomains = async (boxBotId) => {
    const res = await ajax({
      url: '/bot/config/getAllAgentTemplates',
      data: {
        botId: boxBotId
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      setDomains(
        (res.data?.data || []).map((it) => {
          return {
            label: it.agentName,
            value: it.id
          }
        })
      )
    }
  }

  const getFlows = async (boxBotId) => {
    const parentData = window.microApp?.getData()
    setLoading(true)
    const res = await ajax({
      url: '/bot/config/getBotWorkFlows',
      data: {
        botId: boxBotId,
        pageIndex: 1,
        pageSize: 1000
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      const originData = (res.data?.data?.flows || [])
        .filter((it) => {
          if (APP_ENV === 'auto') {
            if (it.official) {
              return true
            } else {
              if (parentData?.orgCode) {
                return it.userGroup === `auto:${parentData?.orgCode}`
              } else {
                return true
              }
            }
          } else {
            return true
          }
        })
        .map((it) => {
          return {
            ...it,
            domains: it.classifyType === 1 ? it.domains : undefined
          }
        })
      originFlows.current = cloneDeep(originData)
      setFlows(
        originData.sort((a, b) => {
          if (a.selected === b.selected) {
            return 0 // 如果 quote 值相同，保持原有顺序
          }
          return a.selected ? -1 : 1 // 如果 a.quote 为 true，a 排在前面；否则 b 排在前面
        })
      )
      setLoading(false)
    }
  }

  const onClassifyTypeChange = (val, record) => {
    setFlows((flows) =>
      flows.map((it) => {
        return {
          ...it,
          classifyType: it.id === record.id ? val : it.classifyType,
          domains: it.id === record.id ? (val === 2 ? undefined : it.domains) : it.domains,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const onDomainChange = (val, record) => {
    setFlows((flows) =>
      flows.map((it) => {
        return {
          ...it,
          domain: it.id === record.id ? val : it.domain,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const onDomainsChange = (val, record) => {
    setFlows((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          domains: it.id === record.id ? val : it.domains,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const handleCheck = (checked, record) => {
    setFlows((flows) =>
      flows.map((it) => {
        return {
          ...it,
          selected: it.id === record.id ? checked : it.selected
        }
      })
    )
  }

  const handleSave = async () => {
    let addFlows = []
    let delFlows = []
    let updateFlows = []
    console.log(flows, '每一次handleSave的时候打印的flows')

    flows.forEach((f) => {
      const originF = originFlows.current.find((it) => it.id === f.id)
      if (f.selected && !originF.selected) {
        console.log(flows, 'flows` addFLow中的`````````````')
        addFlows.push({
          flowId: f.flowId,
          flowName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
      if (!f.selected && originF.selected) {
        delFlows.push({
          configId: f.configId,
          flowId: f.flowId,
          flowName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
      if (f.selected && originF.selected && f.changed) {
        updateFlows.push({
          configId: f.configId,
          flowId: f.flowId,
          flowName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
    })

    if (addFlows.length === 0 && delFlows.length === 0 && updateFlows.length === 0) {
      return message.warning('无配置更新')
    }

    let params = {
      botId: agentDetail?.boxBot?.botId,
      addFlows,
      delFlows,
      updateFlows
    }
    const res = await ajax({
      url: '/bot/config/saveWorkflowConfig',
      data: params,
      method: 'post'
    })
    if (res.data.code === '0') {
      if (APP_ENV === 'auto') {
        window.microApp?.dispatch({ type: 'UPDATE_WORKFLOW_CONFIG', data: params })
      }
      message.success('保存成功')
      onRefresh()
      onClose()
    }
  }

  const onRelateClick = (record) => {
    setBussinessId(record.flowId)
    setSelectIntents(record.intents)
    setIntentModalOpen(true)
  }

  const onIntentModalCancel = () => {
    setIntentModalOpen(false)
  }

  const [selectIntents, setSelectIntents] = useState([])

  const onIntentSubmit = (flowId, intents) => {
    console.log('工作意图modal提交的intents和flowId', intents, flowId)
    setFlows((flows) =>
      flows.map((it) => {
        return {
          ...it,
          intentCount: it.flowId === flowId ? intents.length : it.intentCount,
          intents: it.flowId === flowId ? intents : it?.intents || [],
          changed: it.flowId === flowId ? true : it.changed
        }
      })
    )

    setIntentModalOpen(false)

    // console.log(flows, '每一次onIntentSubmit后重新setFlow后打印的flows')
  }

  const gotoCreate = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/flow')
    } else if (APP_ENV === 'auto') {
      // navigate('/flow')
      baseRouter.push('/agent/flow')
    }
  }

  const onSearch = (value) => {
    const searchItems = originFlows.current.filter((it) =>
      it.name.toLowerCase().includes(value.toLowerCase())
    )
    setCurrentPage(1)
    setFlows(searchItems)
  }

  return (
    <>
      <MyModal
        title="添加工作流"
        visible={visible}
        onCancel={onClose}
        onOk={handleSave}
        width={1000}
        destroyOnHidden
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <Flex justify={'space-between'} align={'center'} style={{ marginBottom: 10 }}>
          <Button type="link" onClick={gotoCreate}>{`创建工作流`}</Button>

          <Search
            placeholder="输入工作流中文名称搜索"
            onSearch={onSearch}
            allowClear
            style={{ width: 300 }}
          />
        </Flex>
        {currentData.length > 0 ? (
          <>
            {currentData.map((item) => (
              <div key={item.id} className={styles.itemCard}>
                <Checkbox
                  checked={item.selected}
                  onChange={(e) => handleCheck(e.target.checked, item)}
                  className={styles.checkbox}
                ></Checkbox>

                <div className={styles.content}>
                  <div className={styles.title}>
                    {item.name}
                    {/* <span className={styles.subtitle}>
                  <Tag color={PLUGIN_COLOR_MAP[item.pluginType]} icon={<TagOutlined />}>
                    {PLUGINS_MAP[item.pluginType]}
                  </Tag>
                </span> */}
                  </div>

                  <Tooltip title={item?.description?.length > 25 ? item.description : ''}>
                    <div className={styles.description}>{item.description}</div>
                  </Tooltip>
                </div>

                <Button
                  onClick={() => onRelateClick(item)}
                  className={styles.button}
                  icon={<IconReactAi />}
                >{`${item.intentCount} 个意图`}</Button>

                <Select
                  value={item.classifyType}
                  style={{ width: 100 }}
                  key={item.id}
                  placeholder="请选择"
                  onChange={(val) => onClassifyTypeChange(val, item)}
                  className={styles.select}
                >
                  <Option value={1}>场景</Option>
                  <Option value={2}>三方</Option>
                </Select>

                {/* <Select
              value={item.classifyType === 1 ? item.domain : null}
              style={{ width: 130 }}
              placeholder="请选择"
              disabled={item.classifyType === 2}
              onChange={(val) => onDomainChange(val, item)}
              className={styles.select}
            >
              {domains.map(({ label, value }) => (
                <Option key={label} value={label}>
                 
                </Option>
              ))}
            </Select> */}
                <SceneLocalizationSelector
                  value={item.domains}
                  domains={domains}
                  onChange={(val) => onDomainsChange(val, item)}
                  disabled={item.classifyType !== 1}
                />
              </div>
            ))}
            {/* 分页器 */}
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={flows.length}
              onChange={(page) => setCurrentPage(page)}
              style={{ marginTop: 16, textAlign: 'center' }}
              align="end"
              hideOnSinglePage={true}
            />
          </>
        ) : loading ? (
          <CenterLoading height={300} />
        ) : (
          <CustomEmpty />
        )}
      </MyModal>
      <IntentModal
        bussinessType="flow"
        bussinessId={bussinessId}
        open={intentModalOpen}
        botId={agentDetail?.boxBot?.botId}
        onCancel={onIntentModalCancel}
        onSubmit={onIntentSubmit}
        selectIntents={selectIntents}
      />
    </>
  )
}

export default WorkflowConfigModal
