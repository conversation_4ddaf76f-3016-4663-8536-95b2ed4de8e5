import { <PERSON><PERSON><PERSON>, Col, Row, Space, Button, Switch, Select } from 'antd'
import styles from './style.module.scss'
import { useEffect, useState } from 'react'
import { useConfigRightResource } from '../hooks/useConfigRight'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { SEARCH_TYPE__OPTIONS, STRATEGY_OPTIONS } from '../constant'

import ajax from '@/utils/http'

import { RightOutlined } from '@ant-design/icons'

function SearchConfig() {
  const [activeKey, setActiveKey] = useState(['search'])

  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const routerId = useAgentDetailStore((state) => state.routerId)

  const [searchType, setSearchType] = useState('')
  const [strategy, setStrategy] = useState('serial')

  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && routerId) {
      getSearchConfig(agentDetail?.boxBot?.botId, routerId)
    }
  }, [agentDetail?.boxBot?.botId, routerId])

  const getSearchConfig = async (boxBotId, routerId) => {
    const res = await ajax({
      url: '/bot/config/getAbleAndSearchConfig',
      data: {
        botId: boxBotId,
        routerId
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      const searchType = res.data?.data?.searchType
      const strategy = res.data?.data?.strategy
      setSearchType(searchType)
      setStrategy(strategy)
    }
  }

  const onSearchTypeChange = (val) => {
    onSearchChange(val, strategy)
  }

  const onStrategyChange = (val) => {
    onSearchChange(searchType, val)
  }

  const onSearchCheckedChange = (checked, event) => {
    event.stopPropagation()
    if (checked) {
      onSearchChange('SearchAggSummary', 'serial')
    } else {
      onSearchChange('Close', 'serial')
    }
  }

  const onSearchChange = async (searchType, strategy) => {
    setLoading(true)
    try {
      const res = await ajax({
        url: '/bot/config/saveAbleAndSearchConfig',
        data: {
          botId: agentDetail?.boxBot?.botId,
          searchType,
          strategy
        },
        method: 'post'
      })
      if (res.data.code === '0') {
        setSearchType(searchType)
        setStrategy(strategy)
      }
    } catch (e) {
    } finally {
      setLoading(false)
    }
  }

  const items = [
    {
      key: 'search',

      label: (
        <div className={styles.labelWithIcon}>
          <RightOutlined
            className={activeKey.includes('search') ? styles.arrowExpanded : styles.arrow}
          />
          <span>搜索推荐</span>
        </div>
      ),

      extra: (
        <Space size={4}>
          <Switch
            checked={searchType && searchType !== 'Close'}
            loading={loading}
            onChange={onSearchCheckedChange}
          />
        </Space>
      ),

      children:
        searchType && searchType !== 'Close' ? (
          <Space className="mt-[16px] mb-[26px] flex" styles={{ item: { flex: 1 } }}>
            <Select
              value={searchType}
              className="w-full"
              placeholder="请选择"
              onChange={onSearchTypeChange}
            >
              {SEARCH_TYPE__OPTIONS.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
            <Select
              value={strategy}
              className="w-full"
              placeholder="请选择"
              onChange={onStrategyChange}
            >
              {STRATEGY_OPTIONS.map((item) => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Space>
        ) : null
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        size="small"
        ghost
        activeKey={activeKey}
        onChange={onChange}
        expandIcon={() => null}
      />
    </>
  )
}

export default SearchConfig
