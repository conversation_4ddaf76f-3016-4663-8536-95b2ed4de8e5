.cardListContainer {
}

.scrollContainer {
  height: calc(100vh - 165.89px);
  overflow-y: auto;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  h2 {
    font-size: 22px;
    color: var(--sparkos-text-color);
    font-weight: 600;
  }
  .actions {
    display: flex;
    align-items: center;
  }
}

.card-wrapper {
  // height: calc(100vh - 250px);

  padding: 0 30px;
}
.card-list {
  display: grid;
  justify-content: space-between;
  grid-template-columns: repeat(3, 1fr); /* 每行放3个卡片，列宽均等 */
  grid-gap: 18px; /* 设置行间和列间的间距 */

  .card-item-wrapper {
    // padding: 30px;
    overflow: visible;
    // margin: -20px;
  }
  .card-item {
    position: relative;
    border-radius: 12px;
    min-height: 140px;
    display: flex;
    cursor: pointer;

    padding: 16px 24px;
    box-sizing: border-box; /* 确保padding不会影响卡片尺寸 */

    // background: linear-gradient(180deg, #eeefff, #ffffff 100%, #ffffff 100%);
    background: var(--sparkos-card-background);
    border: 1px solid var(--sparkos-card-border-color);
    border-radius: var(--sparkos-card-border-radius);

    &:hover {
      box-shadow: var(--sparkos-card-hover-box-shadow);
      // box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 6px -1px rgba(0,0,0,0.10);
      .card-buttons {
        display: flex;
      }
    }

    .card-icon {
      display: block;
      width: 60px;
      height: 60px;
      min-width: 60px;
      border-radius: 12px;
      background-repeat: no-repeat;
      background-size: contain;
      margin-right: 16px;
    }
    .card-content {
      flex: 1;
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      .card-title {
        font-size: 18px;
        font-weight: 500;
        font-size: 16px;
        font-weight: 500;
        color: var(--sparkos-text-color);
        line-height: 19px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
      }

      .card-id {
        font-size: 14px;
        font-weight: 400;
        color: var(--sparkos-desc-color);
        line-height: 16px;
        margin-top: 8px;
        display: flex;
        align-items: center;
        .card-id-content {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
          display: inline-block;
        }
      }

      .card-desc {
        font-size: 14px;
        font-weight: 400;
        color: var(--sparkos-desc-color);
        line-height: 16px;
        height: 32px;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 限制显示的行数 */
        -webkit-box-orient: vertical; /* 垂直方向的盒子布局 */
      }

      .card-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 24px;
      }
      .card-date {
        display: flex;
        align-items: center;
      }
      .time-wrapper {
        white-space: nowrap;
      }
    }
    .card-buttons {
      // display: none;
      // position: absolute;
      // z-index: 1;
      // bottom: 20px;
      // right: 20px;
      .card-button {
        width: 28px;
        height: 28px;
        background: #f5f5f5;
        border-radius: 4px;
        line-height: 30px;
        // font-size: 12px;
        cursor: pointer;
        text-align: center;
        // font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .card-button-copy {
        // color: rgba(55, 59, 67, 0.8);
        background: #f5f5ff;
        color: var(--sparkos-primary-color);
        &:hover {
          background: #dce3ff;
        }
      }

      .card-button-delete {
        color: rgba(55, 59, 67, 0.8);
        background: #fff;
        &:hover {
          background: #fef3f2;
          color: #ed4b3f;
        }
      }
    }
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
}

.loadingMore {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--sparkos-desc-color);
  margin-top: 16px;
}

.noMore {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--sparkos-desc-color);
  margin-top: 16px;
}
